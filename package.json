{"name": "credit-scoring-dashboard", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "cors": "^2.8.5", "express": "^5.1.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-scripts": "^5.0.1", "web-vitals": "^2.1.4", "pg": "^8.11.0", "dotenv": "^16.3.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "proxy": "http://localhost:8000", "eslintConfig": {"extends": ["react-app"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}