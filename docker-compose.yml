version: '3.8'

services:
  # Base de données PostgreSQL
  db:
    image: postgres:15
    container_name: postgres-db
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
      POSTGRES_DB: ${POSTGRES_DB:-credit_dashboard}
    volumes:
      - db-data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - app-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-credit_dashboard}"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # pgAdmin pour administrer PostgreSQL
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_EMAIL:-<EMAIL>}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD:-admin}
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    volumes:
      - pgadmin-data:/var/lib/pgadmin
    ports:
      - "5050:80"
    networks:
      - app-network
    depends_on:
      - db
    restart: unless-stopped

  # Backend FastAPI
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: fastapi-backend
    environment:
      - NODE_ENV=production
      - DATABASE_URL=postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-postgres}@db:5432/${POSTGRES_DB:-credit_dashboard}
      - DB_HOST=db
      - DB_PORT=5432
    ports:
      - "8000:8000"
    networks:
      - app-network
    depends_on:
      db:
        condition: service_healthy
    restart: unless-stopped

  # Frontend React avec Nginx
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: react-frontend
    ports:
      - "3000:80"
    networks:
      - app-network
    depends_on:
      - backend
    restart: unless-stopped

# Réseaux
networks:
  app-network:
    driver: bridge

# Volumes persistants
volumes:
  db-data:
    driver: local
  pgadmin-data:
    driver: local
