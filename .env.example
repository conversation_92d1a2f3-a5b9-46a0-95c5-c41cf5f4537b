# Configuration de la base de données PostgreSQL
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_secure_password_here
POSTGRES_DB=credit_dashboard

# Configuration pgAdmin
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=admin_secure_password

# Configuration du backend
NODE_ENV=production

# URL de connexion à la base de données (pour Docker Compose)
# Le nom d'hôte 'db' correspond au service PostgreSQL dans docker-compose.yml
DATABASE_URL=*******************************************************/credit_dashboard

