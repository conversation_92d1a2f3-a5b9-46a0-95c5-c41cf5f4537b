POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=credit_dashboard
# Copy this file to .env and update the connection string to match your Postgres instance.
# Example formats:
# Local default (username: postgres, password: postgres, db: credit_dashboard):
# DATABASE_URL=postgres://postgres:postgres@localhost:5432/credit_dashboard
# Heroku-style (with ssl):
# DATABASE_URL=**************************************/dbname?sslmode=require

DATABASE_URL=postgres://postgres:postgres@localhost:5432/credit_dashboard

