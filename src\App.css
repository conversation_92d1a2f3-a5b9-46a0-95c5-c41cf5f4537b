/* Modern Credit Scoring Dashboard Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Color Palette */
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);

  /* Neutral Colors */
  --bg-primary: #0f1419;
  --bg-secondary: #1a1f2e;
  --bg-card: rgba(255, 255, 255, 0.05);
  --bg-card-hover: rgba(255, 255, 255, 0.08);
  --text-primary: #ffffff;
  --text-secondary: #a0a9c0;
  --text-muted: #6b7280;
  --border-color: rgba(255, 255, 255, 0.1);
  --border-hover: rgba(255, 255, 255, 0.2);

  /* Shadows */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.2);
  --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.3);

  /* Spacing */
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 3rem;

  /* Border Radius */
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 24px;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  overflow-x: hidden;
}

.App {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f1419 0%, #1a1f2e 50%, #2d1b69 100%);
  position: relative;
}

.App::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: var(--spacing-lg);
  position: relative;
  z-index: 1;
}

/* Modern Card Styles */
.card {
  background: var(--bg-card);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  margin: var(--spacing-md) 0;
  box-shadow: var(--shadow-lg);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
}

.card:hover {
  background: var(--bg-card-hover);
  border-color: var(--border-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

/* Typography */
h1 {
  font-size: 3rem;
  font-weight: 700;
  text-align: center;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--spacing-lg);
  letter-spacing: -0.02em;
}

h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

/* Modern Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: 12px 24px;
  border: none;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}
.btn-primary {
  background: var(--primary-gradient);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: var(--bg-card);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background: var(--bg-card-hover);
  border-color: var(--border-hover);
}

.btn-success {
  background: var(--success-gradient);
  color: white;
}

.btn-warning {
  background: var(--warning-gradient);
  color: white;
}

.btn-danger {
  background: var(--danger-gradient);
  color: white;
}

/* Loading Animation */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.loading {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.shimmer {
  position: relative;
  overflow: hidden;
}

.shimmer::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: shimmer 2s infinite;
}

/* Responsive Grid */
.grid {
  display: grid;
  gap: var(--spacing-lg);
}

.grid-2 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-4 {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-online {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.status-offline {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.status-warning {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
  border: 1px solid rgba(245, 158, 11, 0.3);
}

/* Form Styles */
.form-group {
  margin-bottom: var(--spacing-md);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-card);
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-select {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-card);
  color: var(--text-primary);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

/* Ensure selects and option text are readable in the prediction form
   Some browsers render option backgrounds as white which can make
   white text invisible. Force a dark background and light text for
   selects and their options inside the prediction form. */
.prediction-form .form-select,
.prediction-form select {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.prediction-form .form-select option,
.prediction-form select option {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.prediction-form .form-select:focus,
.prediction-form select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.08);
}

/* Provide a simple arrow indicator for custom appearing selects */
.prediction-form .form-select-wrapper {
  position: relative;
}
.prediction-form .form-select-wrapper::after {
  content: '\25BE'; /* small down arrow */
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  color: var(--text-secondary);
}

/* Table Styles */
.table-container {
  overflow-x: auto;
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
}

.table {
  width: 100%;
  border-collapse: collapse;
  background: var(--bg-card);
}

.table th {
  background: rgba(255, 255, 255, 0.05);
  padding: 16px;
  text-align: left;
  font-weight: 600;
  color: var(--text-secondary);
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 1px solid var(--border-color);
}

.table td {
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
}

.table tr:last-child td {
  border-bottom: none;
}

.table tr:hover {
  background: rgba(255, 255, 255, 0.02);
}

/* RTL adjustments for tables (when document.dir === 'rtl') */
.rtl-table th,
.rtl-table td {
  text-align: right;
}
.rtl-table {
  direction: rtl;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.text-muted { color: var(--text-muted); }
.text-secondary { color: var(--text-secondary); }
.text-primary { color: var(--text-primary); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }

.d-flex { display: flex; }
.d-grid { display: grid; }
.d-block { display: block; }
.d-none { display: none; }

.align-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.gap-1 { gap: var(--spacing-xs); }
.gap-2 { gap: var(--spacing-sm); }
.gap-3 { gap: var(--spacing-md); }
.gap-4 { gap: var(--spacing-lg); }

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: var(--spacing-sm);
  }

  h1 {
    font-size: 2rem;
  }

  .card {
    padding: var(--spacing-md);
  }

  .grid-2,
  .grid-3,
  .grid-4 {
    grid-template-columns: 1fr;
  }

  .table-container {
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: var(--spacing-xs);
  }

  h1 {
    font-size: 1.75rem;
  }

  .card {
    padding: var(--spacing-sm);
  }

  .btn {
    padding: 10px 20px;
    font-size: 0.75rem;
  }
}

/* Component-specific styles */

/* Dashboard Header */
.dashboard-header {
  margin-bottom: var(--spacing-lg);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
  display: inline-block;
  animation: pulse 2s infinite;
}

/* Stats Cards */
.stats-section {
  margin-bottom: var(--spacing-lg);
}

.stat-card {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  background: var(--bg-card-hover);
  border-color: var(--border-hover);
  transform: translateY(-2px);
}

.stat-card-inner {
  position: relative;
  z-index: 2;
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  font-weight: 600;
}

.stat-change {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
}

.stat-content {
  margin-bottom: var(--spacing-sm);
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-suffix {
  font-size: 1rem;
  color: var(--text-secondary);
  font-weight: 400;
}

.stat-title {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.stat-progress {
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.stat-progress-bar {
  height: 100%;
  border-radius: 2px;
  transition: width 1s ease;
}

/* Score Gauge */
.score-gauge-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-lg);
}

.gauge-wrapper {
  position: relative;
}

.gauge-svg {
  filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.3));
}

.gauge-details {
  width: 100%;
}

.score-range {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.range-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 0.75rem;
}

.range-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.score-insights {
  border-top: 1px solid var(--border-color);
  padding-top: var(--spacing-md);
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-md);
}

.insight-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.insight-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.insight-value {
  font-size: 1.125rem;
  font-weight: 600;
}

/* Health Check */
.health-metrics {
  margin: var(--spacing-md) 0;
}

.metric-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-md);
}

.metric-item {
  text-align: center;
  padding: var(--spacing-sm);
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
}

.metric-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-bottom: 4px;
  font-weight: 500;
}

.metric-value {
  font-size: 1.125rem;
  font-weight: 600;
}

.health-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.health-details {
  margin-top: var(--spacing-md);
}

.health-summary {
  cursor: pointer;
  font-weight: 600;
  color: var(--text-secondary);
  font-size: 0.875rem;
  padding: var(--spacing-xs) 0;
}

.health-response {
  margin-top: var(--spacing-sm);
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  font-size: 0.75rem;
  overflow-x: auto;
  max-height: 200px;
}

/* Model Info */
.model-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.model-name h3 {
  margin: 0;
  font-size: 1.25rem;
}

.model-version {
  font-size: 0.75rem;
  color: var(--text-secondary);
  background: rgba(255, 255, 255, 0.1);
  padding: 4px 8px;
  border-radius: 12px;
  margin-top: 4px;
  display: inline-block;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-md);
}

.metric-card {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xs);
}



.metric-bar {
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.metric-progress {
  height: 100%;
  border-radius: 2px;
  transition: width 1s ease;
}

.features-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.feature-tag {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid var(--border-color);
}

.target-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.target-name {
  font-weight: 600;
  color: var(--text-primary);
}

/* Feature Importance */
.feature-importance-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.features-chart {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.feature-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.feature-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.feature-name {
  font-weight: 500;
  color: var(--text-primary);
}

.feature-value {
  font-weight: 600;
  font-size: 0.875rem;
}

.feature-bar {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.feature-progress {
  height: 100%;
  border-radius: 4px;
  transition: width 1s ease;
}

.insights-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

/* Feature insights styling - extends base insight-item */
.feature-importance-content .insight-item {
  padding: var(--spacing-sm);
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
}

.insight-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.insight-text {
  font-size: 0.875rem;
  line-height: 1.4;
}

.legend-items {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 0.75rem;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

/* Recent Predictions */
.applicant-id {
  font-family: 'Monaco', 'Menlo', monospace;
}

.score-badge {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 0.875rem;
}

.loan-amount {
  font-weight: 600;
}

.btn-mini {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 4px 8px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-mini:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: var(--border-hover);
}

/* Error States */
.error-message {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  color: #ef4444;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.error-icon {
  font-size: 1.25rem;
}

/* Empty States */
.empty-state {
  text-align: center;
  padding: var(--spacing-xl);
  color: var(--text-muted);
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-md);
  opacity: 0.5;
}

.empty-state h3 {
  margin-bottom: var(--spacing-sm);
  color: var(--text-secondary);
}

/* Dashboard Layout */
.dashboard-column {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.prediction-section {
  grid-column: 1 / -1;
}

/* Responsive adjustments for new components */
@media (max-width: 768px) {
  .model-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .insights-grid {
    grid-template-columns: 1fr;
  }

  .legend-items {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .score-gauge-container {
    gap: var(--spacing-md);
  }

  .gauge-wrapper svg {
    width: 150px;
    height: 150px;
  }
}
/* Prediction Component Styles */
.completeness-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.progress-bar {
  width: 80px;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.prediction-form {
  margin-top: var(--spacing-md);
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-md);
}

.form-actions {
  display: flex;
  justify-content: center;
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--border-color);
}

.prediction-result {
  border-top: 1px solid var(--border-color);
  padding-top: var(--spacing-md);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.result-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.score-display {
  text-align: center;
  padding: var(--spacing-lg);
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
}

.score-value {
  font-size: 4rem;
  font-weight: 700;
  line-height: 1;
  margin-bottom: var(--spacing-xs);
}

.score-label {
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}

.confidence-badge {
  display: inline-block;
  background: rgba(255, 255, 255, 0.1);
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 0.875rem;
  font-weight: 600;
  border: 1px solid var(--border-color);
}

.factors-breakdown {
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  border: 1px solid var(--border-color);
}

.factors-breakdown h4 {
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

.factors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.factor-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm);
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
}

.factor-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.factor-value {
  font-weight: 600;
  font-size: 0.875rem;
}

.recommendations {
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  border: 1px solid var(--border-color);
}

.recommendations h4 {
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

.recommendations-list {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.recommendation-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
}

.rec-icon {
  font-size: 1.125rem;
  flex-shrink: 0;
}

/* Responsive adjustments for prediction form */
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }

  .score-value {
    font-size: 3rem;
  }

  .factors-grid {
    grid-template-columns: 1fr;
  }

  .result-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }
}