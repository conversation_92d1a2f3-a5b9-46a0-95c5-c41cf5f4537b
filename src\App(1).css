.App {
  font-family: "Segoe UI", <PERSON><PERSON><PERSON>, sans-serif;
  background: #f5f7fa;
  margin: 0;
  padding: 20px;
  color: #333;
  min-height: 100vh;
}

.container {
  max-width: 900px;
  margin: 0 auto;
}

.card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  margin: 20px 0;
  padding: 20px;
}

h1 {
  text-align: center;
  color: #2c3e50;
}

h2 {
  margin-top: 0;
  color: #34495e;
}

button {
  background: #3498db;
  border: none;
  border-radius: 6px;
  color: #fff;
  padding: 10px 16px;
  margin: 8px 0;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s;
}

button:hover {
  background: #2980b9;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
}

th, td {
  border: 1px solid #ccc;
  padding: 8px;
  text-align: left;
}

th {
  background-color: #f2f2f2;
}

pre {
  background: #2d3436;
  color: #dfe6e9;
  padding: 12px;
  border-radius: 8px;
  overflow-x: auto;
  font-size: 13px;
}

.btn-group {
  margin-top: 10px;
}
